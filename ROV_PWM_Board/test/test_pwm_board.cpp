#include <Arduino.h>
#include <unity.h>
#include "pwm_controller.h"

// 测试用的PWM引脚
const int TEST_PWM_PINS[6] = {3, 5, 6, 9, 10, 11};

PWMController* testController;

void setUp(void) {
    testController = new PWMController(TEST_PWM_PINS);
    testController->begin();
}

void tearDown(void) {
    delete testController;
}

// 测试PWM控制器初始化
void test_pwm_controller_initialization() {
    for (int i = 0; i < 6; i++) {
        TEST_ASSERT_EQUAL(0, testController->getPWM(i));
    }
}

// 测试设置单个PWM值
void test_set_single_pwm() {
    TEST_ASSERT_TRUE(testController->setPWM(0, 128));
    TEST_ASSERT_EQUAL(128, testController->getPWM(0));
    
    TEST_ASSERT_TRUE(testController->setPWM(5, 255));
    TEST_ASSERT_EQUAL(255, testController->getPWM(5));
}

// 测试无效的PWM值
void test_invalid_pwm_values() {
    TEST_ASSERT_FALSE(testController->setPWM(0, -1));
    TEST_ASSERT_FALSE(testController->setPWM(0, 256));
    TEST_ASSERT_FALSE(testController->setPWM(6, 128)); // 无效通道
    TEST_ASSERT_FALSE(testController->setPWM(-1, 128)); // 无效通道
}

// 测试设置所有PWM值
void test_set_all_pwm() {
    int values[6] = {10, 20, 30, 40, 50, 60};
    TEST_ASSERT_TRUE(testController->setAllPWM(values));
    
    for (int i = 0; i < 6; i++) {
        TEST_ASSERT_EQUAL(values[i], testController->getPWM(i));
    }
}

// 测试停止所有PWM
void test_stop_all_pwm() {
    int values[6] = {100, 150, 200, 250, 128, 64};
    testController->setAllPWM(values);
    
    testController->stopAll();
    
    for (int i = 0; i < 6; i++) {
        TEST_ASSERT_EQUAL(0, testController->getPWM(i));
    }
}

// 测试边界值
void test_boundary_values() {
    TEST_ASSERT_TRUE(testController->setPWM(0, 0));
    TEST_ASSERT_EQUAL(0, testController->getPWM(0));
    
    TEST_ASSERT_TRUE(testController->setPWM(0, 255));
    TEST_ASSERT_EQUAL(255, testController->getPWM(0));
}

void setup() {
    delay(2000); // 等待串口初始化
    
    UNITY_BEGIN();
    
    RUN_TEST(test_pwm_controller_initialization);
    RUN_TEST(test_set_single_pwm);
    RUN_TEST(test_invalid_pwm_values);
    RUN_TEST(test_set_all_pwm);
    RUN_TEST(test_stop_all_pwm);
    RUN_TEST(test_boundary_values);
    
    UNITY_END();
}

void loop() {
    // 测试完成后什么都不做
}
