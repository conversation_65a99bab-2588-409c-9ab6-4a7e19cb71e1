#!/usr/bin/env python3
"""
ROV PWM Board Python Controller
用于通过串口控制Arduino PWM板的Python脚本
"""

import serial
import time
import sys
import threading
from typing import List, Optional

class ROVPWMController:
    def __init__(self, port: str, baudrate: int = 115200, timeout: float = 1.0):
        """
        初始化PWM控制器
        
        Args:
            port: 串口设备路径 (例如: '/dev/ttyUSB0' 或 'COM3')
            baudrate: 波特率，默认115200
            timeout: 串口超时时间
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn: Optional[serial.Serial] = None
        self.connected = False
        
    def connect(self) -> bool:
        """连接到PWM板"""
        try:
            self.serial_conn = serial.Serial(
                self.port, 
                self.baudrate, 
                timeout=self.timeout
            )
            time.sleep(2)  # 等待Arduino重启
            self.connected = True
            print(f"已连接到PWM板: {self.port}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            self.connected = False
            print("已断开连接")
    
    def send_command(self, command: str) -> str:
        """发送命令并获取响应"""
        if not self.connected or not self.serial_conn:
            return "ERROR: 未连接"
        
        try:
            # 发送命令
            self.serial_conn.write((command + '\n').encode())
            
            # 读取响应
            response = self.serial_conn.readline().decode().strip()
            return response
        except Exception as e:
            return f"ERROR: 通信错误 - {e}"
    
    def set_pwm(self, channel: int, value: int) -> bool:
        """
        设置单个PWM通道
        
        Args:
            channel: 通道号 (0-5)
            value: PWM值 (0-255)
        
        Returns:
            bool: 是否成功
        """
        if not (0 <= channel <= 5):
            print("错误: 通道号必须在0-5之间")
            return False
        
        if not (0 <= value <= 255):
            print("错误: PWM值必须在0-255之间")
            return False
        
        command = f"SET,{channel},{value}"
        response = self.send_command(command)
        
        if response.startswith("OK"):
            print(f"通道{channel}设置为{value}")
            return True
        else:
            print(f"设置失败: {response}")
            return False
    
    def set_all_pwm(self, values: List[int]) -> bool:
        """
        设置所有PWM通道
        
        Args:
            values: 6个PWM值的列表
        
        Returns:
            bool: 是否成功
        """
        if len(values) != 6:
            print("错误: 必须提供6个PWM值")
            return False
        
        for value in values:
            if not (0 <= value <= 255):
                print(f"错误: PWM值{value}超出范围(0-255)")
                return False
        
        command = f"ALL,{','.join(map(str, values))}"
        response = self.send_command(command)
        
        if response.startswith("OK"):
            print(f"所有通道已设置: {values}")
            return True
        else:
            print(f"设置失败: {response}")
            return False
    
    def get_status(self) -> str:
        """获取当前PWM状态"""
        return self.send_command("STATUS")
    
    def stop_all(self) -> bool:
        """停止所有PWM输出"""
        response = self.send_command("STOP")
        if response.startswith("OK"):
            print("所有PWM已停止")
            return True
        else:
            print(f"停止失败: {response}")
            return False
    
    def help(self) -> str:
        """获取帮助信息"""
        return self.send_command("HELP")

def breathing_effect(controller: ROVPWMController, duration: float = 10.0):
    """呼吸灯效果演示"""
    import math
    
    print(f"开始呼吸灯效果，持续{duration}秒...")
    start_time = time.time()
    step = 0
    
    while time.time() - start_time < duration:
        # 计算呼吸效果
        angle = (step % 360) * math.pi / 180
        base_value = int((math.sin(angle) + 1) * 127.5)
        
        # 为每个通道设置不同的相位
        values = []
        for i in range(6):
            phase_offset = i * math.pi / 3
            channel_angle = angle + phase_offset
            channel_value = int((math.sin(channel_angle) + 1) * 127.5)
            values.append(channel_value)
        
        controller.set_all_pwm(values)
        time.sleep(0.05)  # 50ms更新间隔
        step += 1
    
    controller.stop_all()
    print("呼吸灯效果结束")

def interactive_mode(controller: ROVPWMController):
    """交互模式"""
    print("\n=== 交互模式 ===")
    print("可用命令:")
    print("  set <channel> <value>  - 设置单个通道")
    print("  all <v1> <v2> ... <v6> - 设置所有通道")
    print("  status                 - 查看状态")
    print("  stop                   - 停止所有PWM")
    print("  breathing              - 呼吸灯演示")
    print("  help                   - 显示Arduino帮助")
    print("  quit                   - 退出")
    
    while True:
        try:
            cmd = input("\nPWM> ").strip().split()
            if not cmd:
                continue
            
            if cmd[0] == "quit":
                break
            elif cmd[0] == "set" and len(cmd) == 3:
                channel = int(cmd[1])
                value = int(cmd[2])
                controller.set_pwm(channel, value)
            elif cmd[0] == "all" and len(cmd) == 7:
                values = [int(x) for x in cmd[1:]]
                controller.set_all_pwm(values)
            elif cmd[0] == "status":
                print(controller.get_status())
            elif cmd[0] == "stop":
                controller.stop_all()
            elif cmd[0] == "breathing":
                breathing_effect(controller)
            elif cmd[0] == "help":
                print(controller.help())
            else:
                print("无效命令，请重试")
        
        except KeyboardInterrupt:
            break
        except ValueError:
            print("参数错误，请检查数值格式")
        except Exception as e:
            print(f"错误: {e}")

def main():
    port = "/dev/tty.wchusbserial140"
    controller = ROVPWMController(port)
    
    if not controller.connect():
        return
    
    try:
        # 显示初始状态
        print(controller.get_status())
        
        # 进入交互模式
        # interactive_mode(controller)
        
        # controller.set_pwm(0, 128)
        controller.set_all_pwm([100, 150, 200, 250, 128, 64])
        
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
