#include <Arduino.h>
#include "pwm_controller.h"
#include "serial_parser.h"

// PWM输出引脚定义
const int PWM_PINS[6] = {3, 5, 6, 9, 10, 11};

// 创建PWM控制器和串口解析器对象
PWMController pwmController(PWM_PINS);
SerialParser serialParser(&pwmController);

// 演示模式变量
bool demoMode = false;
unsigned long lastDemoUpdate = 0;
int demoStep = 0;

void setup() {
  // 初始化PWM控制器
  pwmController.begin();
  
  // 初始化串口解析器
  serialParser.begin(115200);
  
  Serial.println("PWM Demo Mode Available!");
  Serial.println("Send 'DEMO' to start/stop demo mode");
}

void loop() {
  // 处理串口命令
  serialParser.update();
  
  // 检查是否有DEMO命令
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toUpperCase();
    
    if (command == "DEMO") {
      demoMode = !demoMode;
      if (demoMode) {
        Serial.println("Demo mode started - PWM breathing effect");
        demoStep = 0;
        lastDemoUpdate = millis();
      } else {
        Serial.println("Demo mode stopped");
        pwmController.stopAll();
      }
    }
  }
  
  // 运行演示模式
  if (demoMode) {
    runDemo();
  }
}

void runDemo() {
  unsigned long currentTime = millis();
  
  // 每50ms更新一次演示效果
  if (currentTime - lastDemoUpdate >= 50) {
    lastDemoUpdate = currentTime;
    
    // 创建呼吸灯效果
    float angle = (demoStep % 360) * PI / 180.0;
    int baseValue = (sin(angle) + 1) * 127.5; // 0-255范围
    
    // 为每个通道创建不同的相位偏移
    for (int i = 0; i < 6; i++) {
      float phaseOffset = i * PI / 3; // 60度相位差
      float channelAngle = angle + phaseOffset;
      int channelValue = (sin(channelAngle) + 1) * 127.5;
      pwmController.setPWM(i, channelValue);
    }
    
    demoStep++;
    
    // 每2秒打印一次状态
    if (demoStep % 40 == 0) {
      pwmController.printStatus();
    }
  }
}
