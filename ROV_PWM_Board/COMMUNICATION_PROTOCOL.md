# ROV通信协议文档

## 📋 协议概述

### 基本信息
- **通信方式**: 上位机主动询问，下位机被动应答
- **传输介质**: Serial2 (UART)
- **波特率**: 115200
- **数据格式**: 十六进制字节流
- **校验方式**: CRC8校验

### 通信特点
- **可靠性**: CRC8校验确保数据完整性
- **实时性**: 低延迟响应，适合实时控制
- **扩展性**: 命令码设计支持功能扩展

## 📦 数据帧格式

### 请求帧格式（上位机→下位机）
```
+--------+--------+--------+--------+--------+
| 帧头   | 命令码 | 数据长度| 数据   | 校验码 |
| 0xAA   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```

### 应答帧格式（下位机→上位机）
```
+--------+--------+--------+--------+--------+
| 帧头   | 状态码 | 数据长度| 数据   | 校验码 |
| 0x55   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```

### ESP32完成初始化
55 00 02 01 FF CRC

## 🎯 命令定义

### 传感器数据命令

#### 0x01 - 获取所有传感器数据
**请求**: `AA 01 00 01`
**应答**: `55 00 20 [32字节传感器数据] CRC`

**数据格式**:
```
字节 0-3:   温度 (float, 小端序, °C)
字节 4-7:   压力 (float, 小端序, hPa)
字节 8-11:  深度 (float, 小端序, m)
字节 12-15: MPU6050 Roll角度 (float, 小端序, 度)
字节 16-19: MPU6050 Pitch角度 (float, 小端序, 度)
字节 20-23: MPU6050 Yaw角度 (float, 小端序, 度)
字节 24-27: HMC5883 罗盘航向 (float, 小端序, 度)
字节 28-31: ADC电压值 (float, 小端序, V)
```

#### 0x02 - 获取气压计数据
**请求**: `AA 02 00 02`
**应答**: `55 00 0C [12字节气压数据] CRC`

**数据格式**:
```
字节 0-3:  温度 (float, 小端序, °C)
字节 4-7:  压力 (float, 小端序, hPa)
字节 8-11: 深度 (float, 小端序, m)
```

#### 0x03 - 获取姿态数据
**请求**: `AA 03 00 03`
**应答**: `55 00 0C [12字节姿态数据] CRC`

#### 0x04 - 获取罗盘数据
**请求**: `AA 04 00 04`
**应答**: `55 00 10 [16字节罗盘数据] CRC`

**数据格式**:
```
字节 0-3:   磁场X (float, 小端序)
字节 4-7:   磁场Y (float, 小端序)
字节 8-11:  磁场Z (float, 小端序)
字节 12-15: 航向角 (float, 小端序, 度)
```

### 控制命令

#### 0x10 - 设置PWM输出
**请求**: `AA 10 02 [通道] [值] CRC`
**应答**: `55 00 00 CRC`

**参数**:
- 通道: 0-5 (对应6路PWM)
- 值: 0-180 (对应舵机角度或推进器功率)

**示例**: 设置通道0为90度
```
请求: AA 10 02 00 5A CRC
应答: 55 00 00 CRC
```

#### 0x11 - 设置LED
**请求**: `AA 11 04 [R] [G] [B] [亮度] CRC`
**应答**: `55 00 00 CRC`

**参数**:
- R, G, B: 0-255 (RGB颜色值)
- 亮度: 0-255 (整体亮度)

#### 0x12 - 设置继电器
**请求**: `AA 12 01 [状态] CRC`
**应答**: `55 00 00 CRC`

#### 0x13 - 定深
**请求**: `AA 12 01 [状态] CRC`
**应答**: `55 00 00 CRC`

**参数**:
- 状态: 0=关闭, 1=开启

#### 0x14 - 设置深度控制模式
**请求**: `AA 14 01 [模式] CRC`
**应答**: `55 00 01 [当前状态] CRC`

**参数**:
- 模式: 0=关闭深度控制, 1=开启深度控制
- 当前状态: 0=已关闭, 1=已开启

**功能说明**:
- 开启时：ROV自动保持当前深度，通过摇杆left_y微调目标深度
- 关闭时：摇杆left_y直接控制升降电机

**示例**: 开启深度控制
```
请求: AA 14 01 01 CRC
应答: 55 00 01 01 CRC
```

#### 0x15 - 设置航向控制模式
**请求**: `AA 15 01 [模式] CRC`
**应答**: `55 00 01 [当前状态] CRC`

**参数**:
- 模式: 0=关闭航向控制, 1=开启航向控制
- 当前状态: 0=已关闭, 1=已开启

**功能说明**:
- 开启时：ROV自动保持当前航向，通过摇杆left_x微调目标航向
- 关闭时：摇杆left_x直接控制偏航旋转

**示例**: 开启航向控制
```
请求: AA 15 01 01 CRC
应答: 55 00 01 01 CRC
```

#### 0x16 - 获取控制模式状态
**请求**: `AA 16 00 CRC`
**应答**: `55 00 08 [深度控制状态] [航向控制状态] [姿态平衡状态] [前进平衡状态] [目标深度] CRC`

**返回数据格式**:
```
字节 0:     深度控制状态 (0=关闭, 1=开启)
字节 1:     航向控制状态 (0=关闭, 1=开启)
字节 2:     姿态平衡状态 (0=关闭, 1=开启)
字节 3:     前进平衡状态 (0=关闭, 1=开启)
字节 4-7:   目标深度 (float, 小端序, 米)
```

**示例**: 查询控制状态
```
请求: AA 16 00 CRC
应答: 55 00 08 01 01 01 01 [4字节目标深度] CRC
```

#### 0x17 - 设置姿态平衡模式
**请求**: `AA 17 01 [模式] CRC`
**应答**: `55 00 01 [当前状态] CRC`

**参数**:
- 模式: 0=关闭姿态平衡, 1=开启姿态平衡
- 当前状态: 0=已关闭, 1=已开启

**功能说明**:
- 开启时：在定深模式下，使用陀螺仪数据自动调整升降电机转速以保持水平姿态
- 关闭时：升降电机只响应深度控制，不进行姿态修正
- 仅在深度控制开启时生效

**姿态平衡原理**:
- Roll轴控制：检测左右倾斜，自动调整左右升降电机的差速
- 死区设置：±2度内不进行修正，避免小幅抖动
- 修正限制：最大修正量±30，确保控制稳定

**示例**: 开启姿态平衡
```
请求: AA 17 01 01 CRC
应答: 55 00 01 01 CRC
```

#### 0x18 - 设置前进平衡模式
**请求**: `AA 18 01 [模式] CRC`
**应答**: `55 00 01 [当前状态] CRC`

**参数**:
- 模式: 0=关闭前进平衡, 1=开启前进平衡
- 当前状态: 0=已关闭, 1=已开启

**功能说明**:
- 开启时：在前进/后退时，使用陀螺仪俯仰数据自动调整前后电机转速差以保持俯仰平衡
- 关闭时：前后电机只响应摇杆控制，不进行俯仰修正
- 仅在有前进/后退动作时生效（摇杆right_y偏移量>10）

**前进平衡原理**:
- Pitch轴控制：检测抬头/低头，自动调整前后电机的差速
- 抬头时增加前电机功率，低头时增加后电机功率
- 死区设置：±6度内不进行修正，避免小幅抖动
- 修正限制：最大修正量±20，确保控制稳定

**示例**: 开启前进平衡
```
请求: AA 18 01 01 CRC
应答: 55 00 01 01 CRC
```

#### 0x30 - Xbox摇杆控制
**请求**: `AA 30 04 [left_x] [left_y] [right_x] [right_y] CRC`
**应答**: `55 00 00 CRC`

**参数**:
- left_x: 左摇杆X轴 (-100到+100, 有符号字节)
- left_y: 左摇杆Y轴 (-100到+100, 有符号字节)
- right_x: 右摇杆X轴 (-100到+100, 有符号字节)
- right_y: 右摇杆Y轴 (-100到+100, 有符号字节)

**控制映射**:
- left_y: 前进/后退 (正值=前进, 负值=后退)
- left_x: 左右移动 (正值=右移, 负值=左移)
- right_y: 上升/下降 (正值=上升, 负值=下降)
- right_x: 左右旋转 (正值=右转, 负值=左转)

**示例**: 左摇杆前进50%，右摇杆右转30%
```
请求: AA 30 04 00 32 1E 00 CRC
应答: 55 00 00 CRC
```

### 系统命令

#### 0x20 - 获取电压
**请求**: `AA 20 00 20`
**应答**: `55 00 04 [4字节电压] CRC`

**数据格式**:
```
字节 0-3: 电压值 (float, 小端序, V)
```

#### 0x21 - ADC校准
**请求**: `AA 21 04 [4字节参考电压] CRC`
**应答**: `55 00 0C [12字节校准结果] CRC`

**请求参数**:
```
字节 0-3: 参考电压值 (float, 小端序, V) - 万用表测量的实际电压
```

**应答数据格式**:
```
字节 0-3:  ADC测量值 (float, 小端序, V)
字节 4-7:  参考电压值 (float, 小端序, V)
字节 8-11: 校准偏移值 (float, 小端序, V)
```

**使用示例**: 万用表测量4.64V，发送校准命令
```
请求: AA 21 04 [4.64的float字节] CRC
应答: 55 00 0C [测量值][参考值][偏移值] CRC
```

#### 0xFF - 心跳包
**请求**: `AA FF 00 FF`
**应答**: `55 00 04 [4字节时间戳] CRC`

**数据格式**:
```
字节 0-3: 系统运行时间 (uint32_t, 小端序, ms)
```

## 📊 状态码定义

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 0x00 | 成功 | 命令执行成功 |
| 0x01 | 错误 | 一般性错误 |
| 0x02 | 无效命令 | 不支持的命令码 |
| 0x03 | 无效数据 | 数据格式或长度错误 |

## 🔧 CRC8校验算法

```c
uint8_t calculateCRC8(uint8_t* data, uint8_t length) {
    uint8_t crc = 0x00;
    for (uint8_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x07;
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}
```

## 💡 使用示例

### Python上位机示例

```python
import serial
import struct
import time

class ROVProtocol:
    def __init__(self, port, baudrate=115200):
        self.ser = serial.Serial(port, baudrate)
    
    def calculate_crc8(self, data):
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc
    
    def send_command(self, cmd, data=b''):
        frame = bytearray([0xAA, cmd, len(data)])
        frame.extend(data)
        crc = self.calculate_crc8(frame[1:])
        frame.append(crc)
        
        self.ser.write(frame)
        return self.read_response()
    
    def read_response(self):
        # 读取帧头
        header = self.ser.read(1)
        if header != b'\x55':
            return None
            
        # 读取状态码和数据长度
        status = self.ser.read(1)[0]
        data_len = self.ser.read(1)[0]
        
        # 读取数据和校验码
        data = self.ser.read(data_len)
        crc = self.ser.read(1)[0]
        
        # 验证CRC
        frame_data = bytes([status, data_len]) + data
        if self.calculate_crc8(frame_data) != crc:
            return None
            
        return {'status': status, 'data': data}

## 🎮 控制模式使用示例

### Python示例代码

```python
import serial
import struct

class ROVController:
    def __init__(self, port, baudrate=115200):
        self.ser = serial.Serial(port, baudrate)

    def calculate_crc8(self, data):
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc

    def send_command(self, cmd, data=b''):
        frame = bytearray([0xAA, cmd, len(data)])
        frame.extend(data)
        crc = self.calculate_crc8(frame[1:])
        frame.append(crc)
        self.ser.write(frame)
        return self.read_response()

    def read_response(self):
        header = self.ser.read(1)
        if header != b'\x55':
            return None
        status = self.ser.read(1)[0]
        data_len = self.ser.read(1)[0]
        data = self.ser.read(data_len)
        crc = self.ser.read(1)[0]
        return {'status': status, 'data': data}

    def set_depth_control(self, enable):
        """设置深度控制模式"""
        data = bytes([1 if enable else 0])
        response = self.send_command(0x14, data)
        if response and response['status'] == 0:
            current_state = response['data'][0]
            print(f"深度控制: {'开启' if current_state else '关闭'}")
            return current_state == 1
        return False

    def set_heading_control(self, enable):
        """设置航向控制模式"""
        data = bytes([1 if enable else 0])
        response = self.send_command(0x15, data)
        if response and response['status'] == 0:
            current_state = response['data'][0]
            print(f"航向控制: {'开启' if current_state else '关闭'}")
            return current_state == 1
        return False

    def set_attitude_balance(self, enable):
        """设置姿态平衡模式"""
        data = bytes([1 if enable else 0])
        response = self.send_command(0x17, data)
        if response and response['status'] == 0:
            current_state = response['data'][0]
            print(f"姿态平衡: {'开启' if current_state else '关闭'}")
            return current_state == 1
        return False

    def set_forward_balance(self, enable):
        """设置前进平衡模式"""
        data = bytes([1 if enable else 0])
        response = self.send_command(0x18, data)
        if response and response['status'] == 0:
            current_state = response['data'][0]
            print(f"前进平衡: {'开启' if current_state else '关闭'}")
            return current_state == 1
        return False

    def get_control_status(self):
        """获取控制模式状态"""
        response = self.send_command(0x16)
        if response and response['status'] == 0:
            depth_enabled = response['data'][0] == 1
            heading_enabled = response['data'][1] == 1
            attitude_enabled = response['data'][2] == 1
            forward_enabled = response['data'][3] == 1
            target_depth = struct.unpack('<f', response['data'][4:8])[0]
            return {
                'depth_control': depth_enabled,
                'heading_control': heading_enabled,
                'attitude_balance': attitude_enabled,
                'forward_balance': forward_enabled,
                'target_depth': target_depth
            }
        return None

    def joystick_control(self, left_x, left_y, right_x, right_y):
        """发送摇杆控制命令"""
        # 将-100到100的范围转换为有符号字节
        data = struct.pack('bbbb',
                          max(-100, min(100, left_x)),
                          max(-100, min(100, left_y)),
                          max(-100, min(100, right_x)),
                          max(-100, min(100, right_y)))
        return self.send_command(0x30, data)

# 使用示例
if __name__ == "__main__":
    rov = ROVController('/dev/ttyUSB0')  # 根据实际端口修改

    # 开启所有自动控制功能
    rov.set_depth_control(True)      # 定深控制
    rov.set_heading_control(True)    # 定向控制
    rov.set_attitude_balance(True)   # 定深时的姿态平衡
    rov.set_forward_balance(True)    # 前进时的俯仰平衡

    # 查询当前状态
    status = rov.get_control_status()
    if status:
        print(f"深度控制: {status['depth_control']}")
        print(f"航向控制: {status['heading_control']}")
        print(f"姿态平衡: {status['attitude_balance']}")
        print(f"前进平衡: {status['forward_balance']}")
        print(f"目标深度: {status['target_depth']:.2f}m")

    # 发送摇杆控制命令（在自动控制模式下微调）
    rov.joystick_control(0, 10, 0, 0)  # 轻微上升

    # 关闭自动控制，切换到手动模式
    rov.set_depth_control(False)
    rov.set_heading_control(False)
    rov.set_attitude_balance(False)
    rov.set_forward_balance(False)
```

### 控制模式说明

#### 深度控制模式
- **开启时**: ROV自动保持当前深度，left_y摇杆用于微调目标深度
- **关闭时**: left_y摇杆直接控制升降电机
- **适用场景**: 需要在固定深度进行作业时

#### 航向控制模式
- **开启时**: ROV自动保持当前航向，left_x摇杆用于微调目标航向
- **关闭时**: left_x摇杆直接控制偏航旋转
- **适用场景**: 需要保持固定方向进行直线运动时

#### 姿态平衡模式
- **开启时**: 在定深模式下，使用MPU6050陀螺仪自动调整升降电机转速
- **关闭时**: 升降电机只响应深度控制，不进行姿态修正
- **工作原理**:
  - 检测Roll轴倾斜角度（左右倾斜）
  - 自动调整左右升降电机的差速来保持水平
  - 死区±8度，避免小幅抖动
  - 最大修正±15，确保稳定性
- **适用场景**: 需要保持水平姿态进行精确作业时

#### 前进平衡模式
- **开启时**: 在前进/后退时，使用MPU6050陀螺仪自动调整前后电机转速差
- **关闭时**: 前后电机只响应摇杆控制，不进行俯仰修正
- **工作原理**:
  - 检测Pitch轴俯仰角度（抬头/低头）
  - 抬头时增加前电机功率，低头时增加后电机功率
  - 死区±6度，避免小幅抖动
  - 最大修正±20，确保稳定性
  - 仅在前进/后退动作时生效（摇杆偏移>10）
- **适用场景**: 前进过程中需要保持水平姿态时

#### 组合使用
- 可以同时开启深度控制、航向控制、姿态平衡和前进平衡
- 四种模式协同工作，提供最稳定的控制体验
- 操作员只需关注前进/后退和左右平移
- 适合精确定位和复杂作业场景

#### 推荐配置
- **巡航模式**: 深度控制 + 航向控制 + 前进平衡
- **精确作业**: 深度控制 + 航向控制 + 姿态平衡 + 前进平衡
- **悬停作业**: 深度控制 + 航向控制 + 姿态平衡
- **手动操作**: 全部关闭，完全手动控制
    
    def get_pressure_data(self):
        response = self.send_command(0x02)
        if response and response['status'] == 0x00:
            data = response['data']
            temp = struct.unpack('<f', data[0:4])[0]
            pressure = struct.unpack('<f', data[4:8])[0]
            depth = struct.unpack('<f', data[8:12])[0]
            return {'temperature': temp, 'pressure': pressure, 'depth': depth}
        return None

# 使用示例
rov = ROVProtocol('/dev/ttyACM0')
pressure_data = rov.get_pressure_data()
print(f"温度: {pressure_data['temperature']:.2f}°C")
print(f"压力: {pressure_data['pressure']:.2f} hPa")
print(f"深度: {pressure_data['depth']:.2f} m")
```

## 🚀 性能特性

- **响应时间**: < 10ms
- **数据完整性**: CRC8校验，错误率 < 0.01%
- **吞吐量**: 约1000帧/秒
- **可靠性**: 支持重传和错误恢复

## 📡 数据处理机制

### 接收缓冲区管理
- **缓冲区大小**: 64字节
- **智能帧头搜索**: 连续搜索AA帧头，不依赖首字节位置
- **数据对齐**: 自动移除无效数据，保持数据流连续性
- **多帧处理**: 支持单次接收多个完整帧

### 错误恢复机制
- **CRC校验失败**: 继续搜索下一个有效帧，不中断数据流
- **缓冲区溢出**: 智能保留最后8字节，防止数据丢失
- **超时清理**: 500ms超时自动清理不完整数据
- **帧头丢失**: 保留最后3字节，可能包含不完整帧头

### 调试功能
- **调试开关**: `DEBUG_SERIAL_PROTOCOL` 宏控制调试输出
- **详细日志**: 帧搜索、数据移动、错误处理的详细信息
- **状态监控**: 缓冲区状态和数据流状态实时监控

## 🔍 调试建议

1. **串口监视器**: 使用115200波特率监控调试信息
2. **帧格式检查**: 确保帧头和CRC正确
3. **超时处理**: 设置合理的响应超时时间
4. **错误重试**: 实现命令重发机制
5. **数据连续性**: 确保数据流连续，避免长时间中断
