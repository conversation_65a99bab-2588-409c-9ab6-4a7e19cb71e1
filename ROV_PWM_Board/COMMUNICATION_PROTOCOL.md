# ROV通信协议文档

## 📋 协议概述

### 基本信息
- **通信方式**: 上位机主动询问，下位机被动应答
- **传输介质**: Serial2 (UART)
- **波特率**: 115200
- **数据格式**: 十六进制字节流
- **校验方式**: CRC8校验

### 通信特点
- **可靠性**: CRC8校验确保数据完整性
- **实时性**: 低延迟响应，适合实时控制
- **扩展性**: 命令码设计支持功能扩展

## 📦 数据帧格式

### 请求帧格式（上位机→下位机）
```
+--------+--------+--------+--------+--------+
| 帧头   | 命令码 | 数据长度| 数据   | 校验码 |
| 0xAA   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```

### 应答帧格式（下位机→上位机）
```
+--------+--------+--------+--------+--------+
| 帧头   | 状态码 | 数据长度| 数据   | 校验码 |
| 0x55   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```


## 🎯 命令定义

### 传感器数据命令


#### 0x10 - 设置PWM输出
**请求**: `AA 10 02 [通道] [值] CRC`
**应答**: `55 00 00 CRC`

**参数**:
- 通道: 0-5 (对应6路PWM)
- 值: 0-180 (对应舵机角度或推进器功率)

**示例**: 设置通道0为90度
```
请求: AA 10 02 00 5A CRC
应答: 55 00 00 CRC
```
#### 0x11 - 设置PWM输出
**请求**: `AA 11 0C [值] [值] [值] [值] [值] [值] CRC`
**应答**: `55 00 00 CRC`
