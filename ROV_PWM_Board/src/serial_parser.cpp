#include "serial_parser.h"

SerialParser::SerialParser(PWMController* controller) {
    pwmController = controller;
    resetFrameState();
}

void SerialParser::begin(long baudRate) {
    Serial.begin(baudRate);
    Serial.println("ROV PWM Board - Protocol Mode");
    Serial.println("Waiting for protocol frames...");
}

void SerialParser::update() {
    while (Serial.available()) {
        uint8_t byte = Serial.read();
        handleByte(byte);
    }
}

void SerialParser::handleByte(uint8_t byte) {
    switch (frameState) {
        case WAIT_HEADER:
            if (byte == FRAME_HEADER_REQUEST) {
                frameBuffer[0] = byte;
                frameIndex = 1;
                frameState = WAIT_COMMAND;
            }
            break;

        case WAIT_COMMAND:
            commandCode = byte;
            frameBuffer[frameIndex++] = byte;
            frameState = WAIT_LENGTH;
            break;

        case WAIT_LENGTH:
            dataLength = byte;
            frameBuffer[frameIndex++] = byte;
            if (dataLength == 0) {
                frameState = WAIT_CRC;
            } else if (dataLength <= MAX_FRAME_SIZE - 4) { // 4 = header + cmd + len + crc
                frameState = WAIT_DATA;
            } else {
                // 数据长度超出限制，重置状态
                resetFrameState();
            }
            break;

        case WAIT_DATA:
            frameBuffer[frameIndex++] = byte;
            if (frameIndex >= 3 + dataLength) { // 3 = header + cmd + len
                frameState = WAIT_CRC;
            }
            break;

        case WAIT_CRC:
            expectedCRC = byte;
            frameBuffer[frameIndex++] = byte;
            processFrame();
            resetFrameState();
            break;
    }
}

void SerialParser::processFrame() {
    // 计算CRC (不包括CRC字节本身)
    uint8_t calculatedCRC = calculateCRC8(frameBuffer, frameIndex - 1);

    if (calculatedCRC != expectedCRC) {
        // CRC校验失败，发送错误应答
        sendResponse(STATUS_ERROR);
        return;
    }

    // 根据命令码处理不同的命令
    uint8_t* dataPtr = &frameBuffer[3]; // 数据从第4个字节开始

    switch (commandCode) {
        case CMD_SET_SINGLE_PWM:
            handleSetSinglePWM(dataPtr, dataLength);
            break;

        case CMD_SET_ALL_PWM:
            handleSetAllPWM(dataPtr, dataLength);
            break;

        default:
            // 未知命令，发送错误应答
            sendResponse(STATUS_ERROR);
            break;
    }
}

void SerialParser::handleSetSinglePWM(uint8_t* data, uint8_t length) {
    if (length != 2) {
        sendResponse(STATUS_ERROR);
        return;
    }

    uint8_t channel = data[0];
    uint8_t angle = data[1];

    // 验证通道号
    if (channel >= PWM_CHANNELS) {
        sendResponse(STATUS_ERROR);
        return;
    }

    // 验证角度范围 (0-180)
    if (angle > 180) {
        sendResponse(STATUS_ERROR);
        return;
    }

    // 转换角度到PWM值并设置
    uint8_t pwmValue = convertAngleToPWM(angle);
    if (pwmController->setPWM(channel, pwmValue)) {
        sendResponse(STATUS_OK);
    } else {
        sendResponse(STATUS_ERROR);
    }
}

void SerialParser::handleSetAllPWM(uint8_t* data, uint8_t length) {
    if (length != PWM_CHANNELS) {
        sendResponse(STATUS_ERROR);
        return;
    }

    // 验证所有角度值
    for (int i = 0; i < PWM_CHANNELS; i++) {
        if (data[i] > 180) {
            sendResponse(STATUS_ERROR);
            return;
        }
    }

    // 转换并设置所有PWM值
    int pwmValues[PWM_CHANNELS];
    for (int i = 0; i < PWM_CHANNELS; i++) {
        pwmValues[i] = convertAngleToPWM(data[i]);
    }

    if (pwmController->setAllPWM(pwmValues)) {
        sendResponse(STATUS_OK);
    } else {
        sendResponse(STATUS_ERROR);
    }
}

uint8_t SerialParser::calculateCRC8(uint8_t* data, uint8_t length) {
    uint8_t crc = 0x00;
    for (uint8_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x07;
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}

void SerialParser::sendResponse(uint8_t statusCode, uint8_t* data, uint8_t dataLen) {
    uint8_t response[MAX_FRAME_SIZE];
    uint8_t responseLen = 0;

    // 构建应答帧
    response[responseLen++] = FRAME_HEADER_RESPONSE;  // 帧头
    response[responseLen++] = statusCode;             // 状态码
    response[responseLen++] = dataLen;                // 数据长度

    // 添加数据
    if (data != nullptr && dataLen > 0) {
        for (uint8_t i = 0; i < dataLen; i++) {
            response[responseLen++] = data[i];
        }
    }

    // 计算并添加CRC
    uint8_t crc = calculateCRC8(response, responseLen);
    response[responseLen++] = crc;

    // 发送应答帧
    Serial.write(response, responseLen);
}

void SerialParser::resetFrameState() {
    frameState = WAIT_HEADER;
    frameIndex = 0;
    commandCode = 0;
    dataLength = 0;
    expectedCRC = 0;
}

uint8_t SerialParser::convertAngleToPWM(uint8_t angle) {
    // 将0-180度转换为0-255的PWM值
    // 0度 -> 0, 90度 -> 127, 180度 -> 255
    return (uint8_t)((angle * 255UL) / 180);
}
