#include "serial_parser.h"

SerialParser::SerialParser(PWMController* controller) {
    pwmController = controller;
    commandReady = false;
    inputBuffer.reserve(200);
}

void SerialParser::begin(long baudRate) {
    Serial.begin(baudRate);
    Serial.println("=== ROV PWM Board v1.0 ===");
    sendHelp();
    pwmController->printStatus();
}

void SerialParser::update() {
    while (Serial.available()) {
        char c = Serial.read();
        handleChar(c);
    }
    
    if (commandReady) {
        processCommand(inputBuffer);
        inputBuffer = "";
        commandReady = false;
    }
}

void SerialParser::handleChar(char c) {
    if (c == '\n' || c == '\r') {
        if (inputBuffer.length() > 0) {
            commandReady = true;
        }
    } else if (c >= 32 && c <= 126) { // 可打印字符
        inputBuffer += c;
    }
}

void SerialParser::processCommand(String command) {
    command.trim();
    command.toUpperCase();
    
    if (command.length() == 0) {
        return;
    }
    
    Serial.print("Received: ");
    Serial.println(command);
    
    if (command.startsWith("SET,")) {
        if (parseSetCommand(command)) {
            pwmController->printStatus();
        }
    }
    else if (command.startsWith("ALL,")) {
        if (parseAllCommand(command)) {
            pwmController->printStatus();
        }
    }
    else if (command == "STATUS") {
        pwmController->printStatus();
    }
    else if (command == "STOP") {
        pwmController->stopAll();
        sendOK("All PWM channels stopped");
        pwmController->printStatus();
    }
    else if (command == "HELP") {
        sendHelp();
    }
    else {
        sendError("Unknown command: " + command);
        sendHelp();
    }
}

bool SerialParser::parseSetCommand(String command) {
    // 格式: SET,channel,value
    int firstComma = command.indexOf(',');
    int secondComma = command.indexOf(',', firstComma + 1);
    
    if (firstComma == -1 || secondComma == -1) {
        sendError("Invalid SET format. Use: SET,channel,value");
        return false;
    }
    
    int channel = command.substring(firstComma + 1, secondComma).toInt();
    int value = command.substring(secondComma + 1).toInt();
    
    if (pwmController->setPWM(channel, value)) {
        sendOK("PWM Channel " + String(channel) + " set to " + String(value));
        return true;
    } else {
        sendError("Invalid channel (0-5) or value (0-255)");
        return false;
    }
}

bool SerialParser::parseAllCommand(String command) {
    // 格式: ALL,v1,v2,v3,v4,v5,v6
    String values = command.substring(4);
    int pwmValues[6];
    int valueIndex = 0;
    int startPos = 0;
    
    for (int i = 0; i < 6; i++) {
        int commaPos = values.indexOf(',', startPos);
        String valueStr;
        
        if (i == 5) { // 最后一个值
            valueStr = values.substring(startPos);
        } else {
            if (commaPos == -1) {
                sendError("Invalid ALL format. Need 6 values: ALL,v1,v2,v3,v4,v5,v6");
                return false;
            }
            valueStr = values.substring(startPos, commaPos);
        }
        
        pwmValues[i] = valueStr.toInt();
        startPos = commaPos + 1;
    }
    
    if (pwmController->setAllPWM(pwmValues)) {
        sendOK("All PWM channels updated");
        return true;
    } else {
        sendError("Invalid PWM values. All values must be 0-255");
        return false;
    }
}

void SerialParser::sendHelp() {
    Serial.println("Available Commands:");
    Serial.println("  SET,channel,value  - Set PWM value (channel: 0-5, value: 0-255)");
    Serial.println("  ALL,v1,v2,v3,v4,v5,v6 - Set all PWM values (0-255 each)");
    Serial.println("  STATUS             - Show current PWM values");
    Serial.println("  STOP               - Set all PWM to 0");
    Serial.println("  HELP               - Show this help");
    Serial.println("Examples:");
    Serial.println("  SET,0,128          - Set channel 0 to 50% PWM");
    Serial.println("  ALL,0,64,128,192,255,0 - Set all channels");
}

void SerialParser::sendError(String message) {
    Serial.print("ERROR: ");
    Serial.println(message);
}

void SerialParser::sendOK(String message) {
    Serial.print("OK: ");
    Serial.println(message);
}

bool SerialParser::isCommandReady() {
    return commandReady;
}

String SerialParser::getCommand() {
    String cmd = inputBuffer;
    inputBuffer = "";
    commandReady = false;
    return cmd;
}
