#include "pwm_controller.h"

PWMController::PWMController(const int pins[NUM_CHANNELS]) {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        pwmPins[i] = pins[i];
        pwmValues[i] = 0;
    }
}

void PWMController::begin() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        pinMode(pwmPins[i], OUTPUT);
        analogWrite(pwmPins[i], 0);
    }
}

bool PWMController::setPWM(int channel, int value) {
    if (!isValidChannel(channel) || !isValidValue(value)) {
        return false;
    }
    
    pwmValues[channel] = value;
    analogWrite(pwmPins[channel], value);
    return true;
}

bool PWMController::setAllPWM(const int values[NUM_CHANNELS]) {
    // 首先验证所有值
    for (int i = 0; i < NUM_CHANNELS; i++) {
        if (!isValidValue(values[i])) {
            return false;
        }
    }
    
    // 如果所有值都有效，则设置它们
    for (int i = 0; i < NUM_CHANNELS; i++) {
        pwmValues[i] = values[i];
        analogWrite(pwmPins[i], values[i]);
    }
    return true;
}

void PWMController::stopAll() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        pwmValues[i] = 0;
        analogWrite(pwmPins[i], 0);
    }
}

int PWMController::getPWM(int channel) {
    if (!isValidChannel(channel)) {
        return -1;
    }
    return pwmValues[channel];
}

void PWMController::updateOutputs() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        analogWrite(pwmPins[i], pwmValues[i]);
    }
}

void PWMController::printStatus() {
    Serial.print("PWM Status: ");
    for (int i = 0; i < NUM_CHANNELS; i++) {
        Serial.print("CH");
        Serial.print(i);
        Serial.print("(Pin");
        Serial.print(pwmPins[i]);
        Serial.print(")=");
        Serial.print(pwmValues[i]);
        if (i < NUM_CHANNELS - 1) Serial.print(", ");
    }
    Serial.println();
}

bool PWMController::isValidValue(int value) {
    return (value >= 0 && value <= 255);
}

bool PWMController::isValidChannel(int channel) {
    return (channel >= 0 && channel < NUM_CHANNELS);
}
