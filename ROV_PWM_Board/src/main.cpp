#include <Arduino.h>
#include "pwm_controller.h"
#include "serial_parser.h"

#include <Servo.h>

Servo myservo;

// PWM输出引脚定义 (Arduino Nano支持PWM的引脚)
const int PWM_PINS[6] = {3, 5, 6, 9, 10, 11};

// 创建PWM控制器和串口解析器对象
// PWMController pwmController(PWM_PINS);
// SerialParser serialParser(&pwmController);

void setup() {
  // 初始化PWM控制器
  // pwmController.begin();

  // 初始化串口解析器
  // serialParser.begin(115200);
}

void loop() {
  // 处理串口命令
  // serialParser.update();

  // int pwmValues[6] = {128, 128, 128, 128, 128, 128};
  // pwmController.setAllPWM(pwmValues); // 测试用

  // 这里可以添加其他循环任务
  // 例如：状态LED闪烁、看门狗喂狗等
}
