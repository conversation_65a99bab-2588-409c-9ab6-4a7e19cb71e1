#ifndef PWM_CONTROLLER_H
#define PWM_CONTROLLER_H

#include <Arduino.h>

// PWM控制器类
class PWMController {
private:
    static const int NUM_CHANNELS = 6;
    int pwmPins[NUM_CHANNELS];
    int pwmValues[NUM_CHANNELS];
    
public:
    // 构造函数
    PWMController(const int pins[NUM_CHANNELS]);
    
    // 初始化PWM引脚
    void begin();
    
    // 设置单个PWM值
    bool setPWM(int channel, int value);
    
    // 设置所有PWM值
    bool setAllPWM(const int values[NUM_CHANNELS]);
    
    // 停止所有PWM输出
    void stopAll();
    
    // 获取PWM值
    int getPWM(int channel);
    
    // 更新硬件PWM输出
    void updateOutputs();
    
    // 打印状态
    void printStatus();
    
    // 验证PWM值范围
    bool isValidValue(int value);
    
    // 验证通道号
    bool isValidChannel(int channel);
};

#endif
