#ifndef SERIAL_PARSER_H
#define SERIAL_PARSER_H

#include <Arduino.h>
#include "pwm_controller.h"

// 协议常量定义
#define FRAME_HEADER_REQUEST    0xAA    // 请求帧头
#define FRAME_HEADER_RESPONSE   0x55    // 应答帧头
#define CMD_SET_SINGLE_PWM      0x10    // 设置单路PWM命令
#define CMD_SET_ALL_PWM         0x11    // 设置所有PWM命令
#define STATUS_OK               0x00    // 成功状态码
#define STATUS_ERROR            0xFF    // 错误状态码

#define MAX_FRAME_SIZE          16      // 最大帧长度
#define PWM_CHANNELS            6       // PWM通道数

// 帧解析状态
enum FrameState {
    WAIT_HEADER,
    WAIT_COMMAND,
    WAIT_LENGTH,
    WAIT_DATA,
    WAIT_CRC
};

// 串口协议解析器类
class SerialParser {
private:
    PWMController* pwmController;

    // 帧解析相关
    FrameState frameState;
    uint8_t frameBuffer[MAX_FRAME_SIZE];
    uint8_t frameIndex;
    uint8_t commandCode;
    uint8_t dataLength;
    uint8_t expectedCRC;

    // CRC8计算
    uint8_t calculateCRC8(uint8_t* data, uint8_t length);

    // 发送应答帧
    void sendResponse(uint8_t statusCode, uint8_t* data = nullptr, uint8_t dataLen = 0);

    // 处理完整的帧
    void processFrame();

    // 处理设置单路PWM命令
    void handleSetSinglePWM(uint8_t* data, uint8_t length);

    // 处理设置所有PWM命令
    void handleSetAllPWM(uint8_t* data, uint8_t length);

    // 重置帧解析状态
    void resetFrameState();

    // 验证PWM值范围 (0-180度转换为0-255)
    uint8_t convertAngleToPWM(uint8_t angle);

public:
    // 构造函数
    SerialParser(PWMController* controller);

    // 初始化
    void begin(long baudRate = 115200);

    // 处理串口数据
    void update();

    // 处理接收到的字节
    void handleByte(uint8_t byte);
};

#endif
