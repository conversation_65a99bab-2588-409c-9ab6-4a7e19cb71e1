#ifndef SERIAL_PARSER_H
#define SERIAL_PARSER_H

#include <Arduino.h>
#include "pwm_controller.h"

// 串口命令解析器类
class SerialParser {
private:
    PWMController* pwmController;
    String inputBuffer;
    bool commandReady;
    
    // 解析SET命令
    bool parseSetCommand(String command);
    
    // 解析ALL命令
    bool parseAllCommand(String command);
    
    // 发送帮助信息
    void sendHelp();
    
    // 发送错误信息
    void sendError(String message);
    
    // 发送成功信息
    void sendOK(String message);
    
public:
    // 构造函数
    SerialParser(PWMController* controller);
    
    // 初始化
    void begin(long baudRate = 115200);
    
    // 处理串口数据
    void update();
    
    // 处理接收到的字符
    void handleChar(char c);
    
    // 处理完整的命令
    void processCommand(String command);
    
    // 检查是否有命令准备好
    bool isCommandReady();
    
    // 获取并清除命令
    String getCommand();
};

#endif
