# ROV PWM Board

这是一个基于Arduino Nano的6通道PWM控制板，通过串口接收命令来控制6个PWM输出。

## 硬件配置

- **微控制器**: <PERSON><PERSON><PERSON><PERSON> (ATmega328P)
- **PWM输出引脚**: 3, 5, 6, 9, 10, 11
- **串口波特率**: 115200
- **PWM分辨率**: 8位 (0-255)

## 引脚映射

| 通道 | Arduino引脚 | 描述 |
|------|-------------|------|
| 0    | 3           | PWM输出通道0 |
| 1    | 5           | PWM输出通道1 |
| 2    | 6           | PWM输出通道2 |
| 3    | 9           | PWM输出通道3 |
| 4    | 10          | PWM输出通道4 |
| 5    | 11          | PWM输出通道5 |

## 串口命令

### 设置单个PWM通道
```
SET,channel,value
```
- `channel`: 通道号 (0-5)
- `value`: PWM值 (0-255)

**示例**:
```
SET,0,128    # 设置通道0为50%占空比
SET,3,255    # 设置通道3为100%占空比
```

### 设置所有PWM通道
```
ALL,v1,v2,v3,v4,v5,v6
```
- `v1-v6`: 6个通道的PWM值 (0-255)

**示例**:
```
ALL,0,64,128,192,255,0    # 设置所有6个通道
```

### 查看当前状态
```
STATUS
```
显示所有通道的当前PWM值。

### 停止所有PWM输出
```
STOP
```
将所有通道的PWM值设置为0。

### 显示帮助信息
```
HELP
```
显示可用命令列表。

## 响应格式

- **成功**: `OK: 描述信息`
- **错误**: `ERROR: 错误信息`
- **状态**: `PWM Status: CH0(Pin3)=128, CH1(Pin5)=64, ...`

## 编译和上传

### 使用PlatformIO
```bash
# 编译
pio run

# 上传到Arduino Nano
pio run --target upload

# 打开串口监视器
pio device monitor
```

### 使用Arduino IDE
1. 打开 `src/main.cpp`
2. 选择板子: Arduino Nano
3. 选择处理器: ATmega328P (Old Bootloader)
4. 编译并上传

## 测试

运行单元测试：
```bash
pio test
```

## 使用示例

### Python控制脚本示例
```python
import serial
import time

# 连接到Arduino
ser = serial.Serial('/dev/ttyUSB0', 115200, timeout=1)
time.sleep(2)  # 等待Arduino重启

# 设置PWM值
ser.write(b'SET,0,128\n')  # 通道0设为50%
ser.write(b'SET,1,255\n')  # 通道1设为100%

# 设置所有通道
ser.write(b'ALL,100,150,200,250,128,64\n')

# 查看状态
ser.write(b'STATUS\n')
response = ser.readline().decode().strip()
print(response)

# 停止所有PWM
ser.write(b'STOP\n')

ser.close()
```

### 串口终端测试
使用任何串口终端工具（如PuTTY、Arduino IDE串口监视器等）：

1. 连接到设备，波特率115200
2. 发送命令测试：
   ```
   HELP
   SET,0,128
   STATUS
   ALL,100,100,100,100,100,100
   STOP
   ```

## 故障排除

1. **串口连接问题**
   - 检查USB线缆连接
   - 确认波特率设置为115200
   - 检查串口权限（Linux/Mac）

2. **PWM输出无效果**
   - 检查引脚连接
   - 确认PWM值范围（0-255）
   - 使用万用表或示波器测量输出

3. **命令无响应**
   - 检查命令格式是否正确
   - 确认以换行符结尾
   - 发送HELP命令查看可用命令

## 扩展功能

可以在代码中添加以下功能：
- PWM频率调节
- 渐变控制
- 预设模式
- 状态LED指示
- 看门狗保护
- EEPROM设置保存
