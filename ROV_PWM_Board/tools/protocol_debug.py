#!/usr/bin/env python3
"""
ROV PWM Board Protocol Debug Tool
用于调试和分析协议通信的工具
"""

import serial
import time
import sys

def calculate_crc8(data):
    """计算CRC8校验码"""
    crc = 0x00
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x07
            else:
                crc <<= 1
            crc &= 0xFF
    return crc

def parse_hex_string(hex_str):
    """解析十六进制字符串为字节数组"""
    hex_str = hex_str.replace(' ', '').replace(',', '')
    if len(hex_str) % 2 != 0:
        print("错误: 十六进制字符串长度必须是偶数")
        return None
    
    try:
        return bytearray.fromhex(hex_str)
    except ValueError:
        print("错误: 无效的十六进制字符串")
        return None

def format_bytes(data):
    """格式化字节数组为十六进制字符串"""
    return ' '.join([f'{b:02X}' for b in data])

def build_frame(command, data_hex=""):
    """构建协议帧"""
    frame = bytearray()
    frame.append(0xAA)  # 帧头
    frame.append(command)  # 命令码
    
    # 解析数据
    if data_hex:
        data = parse_hex_string(data_hex)
        if data is None:
            return None
    else:
        data = bytearray()
    
    frame.append(len(data))  # 数据长度
    frame.extend(data)  # 数据
    
    # 计算CRC
    crc = calculate_crc8(frame)
    frame.append(crc)
    
    return frame

def analyze_frame(frame_hex):
    """分析协议帧"""
    frame = parse_hex_string(frame_hex)
    if frame is None or len(frame) < 4:
        print("错误: 帧长度不足")
        return
    
    print(f"原始帧: {format_bytes(frame)}")
    print(f"帧长度: {len(frame)} 字节")
    
    # 解析帧结构
    header = frame[0]
    cmd_or_status = frame[1]
    data_len = frame[2]
    
    if header == 0xAA:
        print("帧类型: 请求帧")
        print(f"命令码: 0x{cmd_or_status:02X}")
        if cmd_or_status == 0x10:
            print("  -> 设置单路PWM")
        elif cmd_or_status == 0x11:
            print("  -> 设置所有PWM")
        else:
            print("  -> 未知命令")
    elif header == 0x55:
        print("帧类型: 应答帧")
        print(f"状态码: 0x{cmd_or_status:02X}")
        if cmd_or_status == 0x00:
            print("  -> 成功")
        elif cmd_or_status == 0xFF:
            print("  -> 错误")
        else:
            print("  -> 未知状态")
    else:
        print(f"错误: 无效的帧头 0x{header:02X}")
        return
    
    print(f"数据长度: {data_len}")
    
    if len(frame) < 3 + data_len + 1:
        print("错误: 帧长度与数据长度不匹配")
        return
    
    # 提取数据
    if data_len > 0:
        data = frame[3:3+data_len]
        print(f"数据: {format_bytes(data)}")
        
        # 解析数据内容
        if header == 0xAA and cmd_or_status == 0x10 and data_len == 2:
            print(f"  通道: {data[0]}")
            print(f"  角度: {data[1]}度")
        elif header == 0xAA and cmd_or_status == 0x11 and data_len == 6:
            print("  角度值:")
            for i, angle in enumerate(data):
                print(f"    通道{i}: {angle}度")
    
    # 验证CRC
    received_crc = frame[-1]
    calculated_crc = calculate_crc8(frame[:-1])
    print(f"接收CRC: 0x{received_crc:02X}")
    print(f"计算CRC: 0x{calculated_crc:02X}")
    
    if received_crc == calculated_crc:
        print("CRC校验: ✓ 通过")
    else:
        print("CRC校验: ✗ 失败")

def interactive_mode():
    """交互模式"""
    print("=== ROV PWM协议调试工具 ===")
    print("命令:")
    print("  build <cmd> [data]  - 构建协议帧")
    print("  analyze <hex>       - 分析协议帧")
    print("  crc <hex>          - 计算CRC8")
    print("  examples           - 显示示例")
    print("  quit               - 退出")
    print()
    
    while True:
        try:
            cmd_line = input("debug> ").strip().split()
            if not cmd_line:
                continue
            
            cmd = cmd_line[0].lower()
            
            if cmd == "quit":
                break
            elif cmd == "build":
                if len(cmd_line) < 2:
                    print("用法: build <命令码> [数据]")
                    continue
                
                try:
                    command = int(cmd_line[1], 16)
                    data_hex = cmd_line[2] if len(cmd_line) > 2 else ""
                    
                    frame = build_frame(command, data_hex)
                    if frame:
                        print(f"构建的帧: {format_bytes(frame)}")
                except ValueError:
                    print("错误: 无效的命令码")
            
            elif cmd == "analyze":
                if len(cmd_line) < 2:
                    print("用法: analyze <十六进制帧>")
                    continue
                
                frame_hex = ' '.join(cmd_line[1:])
                analyze_frame(frame_hex)
            
            elif cmd == "crc":
                if len(cmd_line) < 2:
                    print("用法: crc <十六进制数据>")
                    continue
                
                data_hex = ' '.join(cmd_line[1:])
                data = parse_hex_string(data_hex)
                if data:
                    crc = calculate_crc8(data)
                    print(f"CRC8: 0x{crc:02X}")
            
            elif cmd == "examples":
                print("示例:")
                print("  build 10 00 5A     # 设置通道0为90度")
                print("  build 11 5A5A5A5A5A5A  # 设置所有通道为90度")
                print("  analyze AA 10 02 00 5A 8C  # 分析请求帧")
                print("  analyze 55 00 00 83        # 分析应答帧")
                print("  crc AA 10 02 00 5A         # 计算CRC")
            
            else:
                print("未知命令，输入 examples 查看示例")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")

def main():
    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == "analyze":
            if len(sys.argv) < 3:
                print("用法: protocol_debug.py analyze <十六进制帧>")
                sys.exit(1)
            frame_hex = ' '.join(sys.argv[2:])
            analyze_frame(frame_hex)
        elif sys.argv[1] == "build":
            if len(sys.argv) < 3:
                print("用法: protocol_debug.py build <命令码> [数据]")
                sys.exit(1)
            try:
                command = int(sys.argv[2], 16)
                data_hex = sys.argv[3] if len(sys.argv) > 3 else ""
                frame = build_frame(command, data_hex)
                if frame:
                    print(format_bytes(frame))
            except ValueError:
                print("错误: 无效的命令码")
        else:
            print("未知命令")
    else:
        # 交互模式
        interactive_mode()

if __name__ == "__main__":
    main()
